<?php
namespace wp_index_generator;
use wp_index_generator\includes\wpig_lib as LIB;
use wp_index_generator\includes\wpig_settings;
/**
 *
 * Plugin Name: Sticky TOC - Advance Table Of Contents
 * Description: Sticky TOC is a powerful Wordpress plugin. It scans headings through the post content automatically and creates the table of contents, Sticky in the page display. It has 10+ design combinations.
 * Version: 1.0.2
 * Author: Ahmadderar
 * AUthor URI: https://Ahmadderar.com
 * License: GPL2+
 * Text Domain: wpi-gen
 * Domain Path: /languages
 */

define("WPIG_V", "1.0.2");
define("WPIG_PATH", plugin_dir_path(__FILE__));
define("WPIG_URL", plugin_dir_url(__FILE__));

if (!defined("ABSPATH")) {
    die();
}

if (!class_exists("WPI_Generator")) {

    class WPI_Generator
    {

        private $table_generated = array("");
        public function __construct()
        {

            add_action("plugin_loaded", array($this, "require_files"));
            add_action("wp_enqueue_scripts", array($this, "enqueue_scripts"));
            add_action( "admin_enqueue_scripts", array($this, "enqueue_admin_scripts") );
            add_shortcode( "stoc" , array( $this, "generate_table" ) );
            add_filter("the_content", array( $this, "intigrate_content"), 90);
            add_filter("the_content", array( "wp_index_generator\includes\wpig_lib","sanitize_headings") , 50);
            add_action( "wp_head", array($this, "integrate_stickyheader") );

        }

        /**
         * This function is hooked to enqueue all required scripts and style for admin settings page
         */
        function enqueue_admin_scripts(){

            if( isset( $_GET['page'] ) && $_GET['page'] == "wpig-main" ){
                wp_enqueue_style( "wpig-settings-style", WPIG_URL . "assets/css/admin-style.css", array(), WPIG_V );

                wp_enqueue_script( "wpig-settings-js", WPIG_URL . "assets/js/admin-script.js", array("jquery"), WPIG_V, true );
                
                $script['codeEditor'] = wp_enqueue_code_editor(array('type' => 'text/css'));
                $script['style'] = lib::wpig_get_option( "style_type", "wpig_adv_settings", "1");
                wp_enqueue_script('wp-theme-plugin-editor');
                wp_enqueue_style('wp-codemirror');
                wp_localize_script( "wpig-settings-js", "STOC", $script );
            }

        }

        /**
         * This is a callback function to exqueue all scripts on front-end
         */
        function enqueue_scripts(){
            wp_enqueue_style( "wpig-common", WPIG_URL . "assets/css/styles.css", array(), "1.2" );
            LIB::generate_css();			
            wp_enqueue_script( "stoc-pro-script", WPIG_URL . "assets/js/script.js", array("jquery"), WPIG_V , true );
            $enable_tgdisplay = LIB::wpig_get_option( "en_toggle_display", "wpig_settings", "true") ;
            $hideByDefault = LIB::wpig_get_option("hideByDefault","wpig_settings","true" );
            $hideByDefault = $enable_tgdisplay == "true" ? $hideByDefault : "false";
            wp_localize_script( "stoc-pro-script" , "STOC", array(
                "ishidden"=>$hideByDefault,
                "toggleSpeed"=> LIB::wpig_get_option( "toggleDisplay_speed", "wpig_settings", 100),
                "isSticky"=>LIB::wpig_get_option( "stickymenu", "wpig_settings", "true")
            ) );
            
            $custom_css = LIB::wpig_get_option("css","wpig_custom_css","");

            if( !empty( $custom_css ) ) {
                wp_add_inline_style( "wpig-common" , $custom_css );
            }

        }

        /**
         * This is a callback function for shortcode
         */
        public function generate_table( $atts, $content = null ){

            // Bail out if try to execute on admin side
            if( is_admin() ) return;

            GLOBAL $post;

            $content = $post->post_content  ;
            $content = LIB::sanitize_headings( $content ) ;
            $this->table_generated[] = $post->ID ;

            str_replace( "[stoc]", "", $content );
            ob_start();
                echo LIB::create_indexing($content);
            $d = ob_get_clean();

             return $d ;
        }


        /**
         * This is a callback function to filter the current post content and include index table
         * 
         * @param string $content The current post content
         * 
         * @return string $content The current post content but with the index table
         */
        public function intigrate_content($content)
        {

            GLOBAL $post;

            if( in_array( $post->ID, $this->table_generated ) ){
                $new_content = $this->integrate_floatingbar( $content, $content );
                return $new_content;
            }

            $this->table_generated[] = $post->ID;
            $is_content = LIB::wpig_get_option( "selectedPost", "wpig_settings", array(""));

            $current_post = get_post_type() ;

            if ( !in_array( $current_post, $is_content ) || ( is_home() || is_archive() ) ) {
                return $content;
            }
            
            $position = LIB::wpig_get_option( "contentPosition", "wpig_settings", "afterfirstheading");

            // Do not make assumption. Check what heading is at the top of the post
            preg_match("</(h[1-9])>",$content,$H);
			
			if (isset($H[1])) {
				$headings = explode("</".$H[1].">", $content);
			} else {
				$headings = array();
			}
            
            if( $position == "afterfirstheading" && count( $headings ) > 1 ){
                $paragraphs_count = count($headings);
                $middle_index = absint(floor($paragraphs_count / 2));

                $new_content = '';
                for ($i = 0; $i < $paragraphs_count; $i++) {
                    if ($i === 1) {
                        $new_content .= "</". $H[1] .">" ;
                        $new_content .= LIB::create_indexing($content) ;
                        $new_content .= str_replace("</". $H[1] .">","", $headings[$i] ) ;
                    }else{
                        $new_content .= "</" . $H[1]. ">";
                        $new_content .= $headings[$i] ;
                    }
                }

            }else if( $position == "afterfirstp" ){
                $paragraphs = explode("</p>", $content);
                $paragraphs_count = count($paragraphs);
                
                $new_content = '';
                for ($i = 0; $i < $paragraphs_count; $i++) {
                    if ($i === 1) {
                        $new_content .= "</p><p>";
                        $new_content .= LIB::create_indexing($content) ;
                        $new_content .= "</p>";
                        $new_content .= str_replace("</p>","", $paragraphs[$i] ) ;
                    }else{
                        $new_content .= $paragraphs[$i] ;
                    }
                }
            }else if( $position == "top" ){
                $new_content = "<p>". LIB::create_indexing($content) . "</p>" . $content;
            }else if( $position == "bottom" ){
                $new_content = $content . "<p>" . LIB::create_indexing($content) . "</p>";
            }

            $new_content = $this->integrate_floatingbar( $content, $new_content );
            return $new_content;

        }


        /**
         * This functions integrate the floating sidebar for the post
         * @param string $content This is the unfiltered and original post content
         * @param string $new_content A filtered or modified content of the post
         */
        function integrate_floatingbar( $content, $new_content = null ){

            $is_sticky = LIB::wpig_get_option("stickymenu", "wpig_settings", "true" );
            $iStyle = LIB::wpig_get_option("style_type", "wpig_adv_settings", "2" );

            if( $is_sticky == "true" ){
                $icon = WPIG_URL . "\assets\menu.png";
                $menu = "<div id='wpig-floatingbar' class='wpig-floatingbar'>";
                $menu .= "<div id='wpig-switch-wrapper' class='wpig-switch-wrapper'><span id='wpig-floating-switch' class='wpig-floating-switch'><img src='". $icon ."'></span></div>";
                $menu .= LIB::create_indexing($content, 3); 
                $menu .= "</div>";
                $new_content = $menu . $new_content ;
            }

            return $new_content;
        }

        /**
         * This functions integrate the sticky header for the post
         * @param string $content A filtered or modified content of the post
         */
        function integrate_stickyheader(){
			
			GLOBAL $post;

      if ( ! $post ) {
        return;
      }

            $content = $post->post_content;
			$content = LIB::sanitize_headings( $content ) ;
			
            $is_sticky_header = LIB::wpig_get_option("stickyheadermenu", "wpig_settings", "true" );
			
			$is_hidden_for_mobile = LIB::wpig_get_option("mobilevisibilty", "wpig_settings", "" );
			
			if( $is_sticky_header == "true" ){
                $sticky_header = "<div id='wpig-stickyheader'>";
                $sticky_header .= LIB::create_indexing($content, 110);
                $sticky_header .= "</div>";
				echo $sticky_header;
            }
			
			if( $is_hidden_for_mobile == "false" ){
                echo '<style>@media only screen and (max-width: 600px) {#wpig-floatingbar, #wpig-contents,#wpig-stickyheader{display: none!important;}}</style>';
            }

        }

        /**
         * This function is hooked to gathered all required files after plugins loaded
         */
        public function require_files()
        {
            require_once WPIG_PATH . "/includes/wpig-lib.php" ;
            require_once WPIG_PATH . "/includes/wpig-settings.php" ;
            wpig_settings::init();
        }

    }

}

new WPI_Generator();
