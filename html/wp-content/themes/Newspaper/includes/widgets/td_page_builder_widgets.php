<?php

class td_block1_widget extends td_block_widget {
    var $td_block_id = 'td_block_1'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block1_widget"); });


class td_block2_widget extends td_block_widget {
    var $td_block_id = 'td_block_2'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block2_widget"); });


class td_block3_widget extends td_block_widget {
    var $td_block_id = 'td_block_3'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block3_widget"); });


class td_block4_widget extends td_block_widget {
    var $td_block_id = 'td_block_4'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block4_widget"); });


class td_block5_widget extends td_block_widget {
    var $td_block_id = 'td_block_5'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block5_widget"); });


class td_block6_widget extends td_block_widget {
    var $td_block_id = 'td_block_6'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block6_widget"); });


class td_block7_widget extends td_block_widget {
    var $td_block_id = 'td_block_7'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block7_widget"); });


class td_block8_widget extends td_block_widget {
    var $td_block_id = 'td_block_8'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block8_widget"); });


class td_block9_widget extends td_block_widget {
    var $td_block_id = 'td_block_9'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block9_widget"); });


class td_block10_widget extends td_block_widget {
    var $td_block_id = 'td_block_10'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block10_widget"); });


class td_block11_widget extends td_block_widget {
    var $td_block_id = 'td_block_11'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block11_widget"); });


class td_block12_widget extends td_block_widget {
    var $td_block_id = 'td_block_12'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block12_widget"); });


class td_block13_widget extends td_block_widget {
    var $td_block_id = 'td_block_13'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block13_widget"); });


class td_block14_widget extends td_block_widget {
    var $td_block_id = 'td_block_14'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block14_widget"); });


class td_block15_widget extends td_block_widget {
    var $td_block_id = 'td_block_15'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block15_widget"); });


class td_block16_widget extends td_block_widget {
    var $td_block_id = 'td_block_16'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block16_widget"); });


class td_block17_widget extends td_block_widget {
    var $td_block_id = 'td_block_17'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block17_widget"); });


class td_block18_widget extends td_block_widget {
    var $td_block_id = 'td_block_18'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block18_widget"); });


class td_block19_widget extends td_block_widget {
    var $td_block_id = 'td_block_19'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block19_widget"); });


class td_block20_widget extends td_block_widget {
    var $td_block_id = 'td_block_20'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block20_widget"); });


class td_block21_widget extends td_block_widget {
	var $td_block_id = 'td_block_21'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block21_widget"); });


class td_block22_widget extends td_block_widget {
    var $td_block_id = 'td_block_22'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block22_widget"); });


class td_block23_widget extends td_block_widget {
    var $td_block_id = 'td_block_23'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block23_widget"); });


class td_block24_widget extends td_block_widget {
    var $td_block_id = 'td_block_24'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block24_widget"); });


class td_block25_widget extends td_block_widget {
    var $td_block_id = 'td_block_25'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block25_widget"); });


class td_block_ad_box_widget extends td_block_widget {
    var $td_block_id = 'td_block_ad_box'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block_ad_box_widget"); });


class td_block_authors_widget extends td_block_widget {
    var $td_block_id = 'td_block_authors'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block_authors_widget"); });


class td_block_popular_categories_widget extends td_block_widget {
    var $td_block_id = 'td_block_popular_categories'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block_popular_categories_widget"); });


class td_block_slide_widget extends td_block_widget {
    var $td_block_id = 'td_block_slide'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block_slide_widget"); });


class td_block_text_with_title_widget extends td_block_widget {
    var $td_block_id = 'td_block_text_with_title'; // change only the block id, the rest is autogenerated
}

add_action('widgets_init', function() { return register_widget("td_block_text_with_title_widget"); });



class td_block_weather_widget extends td_block_widget {
	var $td_block_id = 'td_block_weather'; // change only the block id, the rest is autogenerated
}
add_action('widgets_init', function() { return register_widget("td_block_weather_widget"); });


class td_block_exchange_widget extends td_block_widget {
    var $td_block_id = 'td_block_exchange'; // change only the block id, the rest is autogenerated
}
add_action('widgets_init', function() { return register_widget("td_block_exchange_widget"); });

class td_block_instagram_widget extends td_block_widget {
    var $td_block_id = 'td_block_instagram'; // change only the block id, the rest is autogenerated
}
add_action('widgets_init', function() { return register_widget("td_block_instagram_widget"); });




